import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'models/train_data.dart';
import 'rail_api_service.dart';

class MultipleTrainSelectionScreen extends StatefulWidget {
  final List<TrainData> trains;
  final String fromStation;
  final String toStation;
  final String journeyDate;

  const MultipleTrainSelectionScreen({
    super.key,
    required this.trains,
    required this.fromStation,
    required this.toStation,
    required this.journeyDate,
  });

  @override
  State<MultipleTrainSelectionScreen> createState() => _MultipleTrainSelectionScreenState();
}

class _MultipleTrainSelectionScreenState extends State<MultipleTrainSelectionScreen> {
  final _telegramIdController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final RailApiService _apiService = RailApiService();
  
  Map<String, Set<String>> _selectedClassesByTrain = {};
  Map<String, String?> _chartTimesByTrain = {};
  bool _isLoadingChartTimes = false;
  String? _deviceId;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeSelection();
    _getDeviceId();
  }

  @override
  void dispose() {
    _telegramIdController.dispose();
    super.dispose();
  }

  void _initializeSelection() {
    // Auto-select classes for each train
    for (final train in widget.trains) {
      _selectedClassesByTrain[train.trainNumber] = train.autoSelectedClasses.toSet();
    }
  }

  Future<void> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String deviceId;
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? 'unknown-ios';
      } else {
        deviceId = 'unknown-platform';
      }
      
      setState(() {
        _deviceId = deviceId;
      });
    } catch (e) {
      setState(() {
        _deviceId = 'unknown-device';
      });
    }
  }

  Future<void> _fetchAllChartTimes() async {
    if (_isLoadingChartTimes) return;

    setState(() {
      _isLoadingChartTimes = true;
      _chartTimesByTrain.clear();
    });

    try {
      // Use yesterday's date to get chart time pattern, but we'll adjust the date later
      final dateParts = DateTime.now().subtract(const Duration(days: 1)).toIso8601String().split('T')[0].split('-');
      final formattedDate = '${dateParts[0]}-${dateParts[1]}-${dateParts[2]}';

      for (final train in widget.trains) {
        try {
          final chartTime = await _apiService.getChartTime(
            trainNo: train.trainNumber,
            stationCode: train.fromStnCode,
            journeyDate: formattedDate,
          );

          // Adjust the chart time to be one day before the journey date
          final adjustedChartTime = _adjustChartTimeForJourneyDate(chartTime);

          setState(() {
            _chartTimesByTrain[train.trainNumber] = adjustedChartTime;
          });
        } catch (e) {
          setState(() {
            _chartTimesByTrain[train.trainNumber] = 'Chart time not available';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to fetch chart times: ${e.toString()}'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingChartTimes = false;
      });
    }
  }

  String _adjustChartTimeForJourneyDate(String chartTime) {
    try {
      // Parse the journey date from DD-MM-YYYY format
      final journeyDateParts = widget.journeyDate.split('-');
      final journeyDay = int.parse(journeyDateParts[0]);
      final journeyMonth = int.parse(journeyDateParts[1]);
      final journeyYear = int.parse(journeyDateParts[2]);
      final journeyDate = DateTime(journeyYear, journeyMonth, journeyDay);

      // Calculate the chart preparation date (one day before journey)
      final chartDate = journeyDate.subtract(const Duration(days: 1));

      // Try to parse the chart time to extract the time component
      if (chartTime.contains('T')) {
        // If it's an ISO string format like "2025-07-18T15:42:48.000Z"
        final originalDateTime = DateTime.parse(chartTime);
        final adjustedDateTime = DateTime(
          chartDate.year,
          chartDate.month,
          chartDate.day,
          originalDateTime.hour,
          originalDateTime.minute,
          originalDateTime.second,
        );
        return adjustedDateTime.toIso8601String();
      } else if (chartTime.contains(' ')) {
        // If it's a format like "2025-07-18 15:42:48"
        final parts = chartTime.split(' ');
        if (parts.length == 2) {
          final timePart = parts[1];
          final formattedDate = '${chartDate.year.toString().padLeft(4, '0')}-${chartDate.month.toString().padLeft(2, '0')}-${chartDate.day.toString().padLeft(2, '0')}';
          return '$formattedDate $timePart';
        }
      }

      // If we can't parse the time, return a default chart time
      final defaultChartTime = DateTime(chartDate.year, chartDate.month, chartDate.day, 6, 0, 0);
      return '${defaultChartTime.year.toString().padLeft(4, '0')}-${defaultChartTime.month.toString().padLeft(2, '0')}-${defaultChartTime.day.toString().padLeft(2, '0')} 06:00:00';
    } catch (e) {
      // If anything goes wrong, return the original chart time
      return chartTime;
    }
  }

  String? _validateTelegramId(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your Telegram User ID';
    }
    
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'Telegram ID should contain only numbers';
    }
    
    return null;
  }

  Future<void> _submitSelection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate that at least one train has selected classes
    bool hasValidSelection = false;
    for (final trainNumber in _selectedClassesByTrain.keys) {
      if (_selectedClassesByTrain[trainNumber]!.isNotEmpty) {
        hasValidSelection = true;
        break;
      }
    }

    if (!hasValidSelection) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one class for at least one train'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Check if all chart times are fetched
    bool allChartTimesFetched = true;
    for (final train in widget.trains) {
      if (_selectedClassesByTrain[train.trainNumber]!.isNotEmpty &&
          _chartTimesByTrain[train.trainNumber] == null) {
        allChartTimesFetched = false;
        break;
      }
    }

    if (!allChartTimesFetched) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fetch chart times first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      List<TrainSelection> selections = [];
      
      for (final train in widget.trains) {
        final selectedClasses = _selectedClassesByTrain[train.trainNumber]!;
        if (selectedClasses.isNotEmpty) {
          final selection = TrainSelection(
            userId: _deviceId ?? 'unknown',
            telegramId: _telegramIdController.text.trim(),
            fromStation: widget.fromStation,
            toStation: widget.toStation,
            journeyDate: widget.journeyDate,
            trainNo: train.trainNumber,
            chartPrepTime: _chartTimesByTrain[train.trainNumber] ?? 'Not available',
            selectedClasses: selectedClasses.join(','),
          );
          selections.add(selection);
        }
      }

      // Show the final JSON
      _showFinalResult(selections);
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _showFinalResult(List<TrainSelection> selections) {
    const jsonEncoder = JsonEncoder.withIndent('  ');
    final jsonString = jsonEncoder.convert(selections.map((s) => s.toJson()).toList());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Selection Complete'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Your ${selections.length} train selection(s) have been processed:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SelectableText(
                  jsonString,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).popUntil((route) => route.isFirst); // Go to home
            },
            child: const Text('Done'),
          ),
          ElevatedButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: jsonString));
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Copied to clipboard')),
              );
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  Widget _buildRouteInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Journey Details',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text('From: ${widget.fromStation}'),
            Text('To: ${widget.toStation}'),
            Text('Date: ${widget.journeyDate}'),
            Text('Selected Trains: ${widget.trains.length}'),
          ],
        ),
      ),
    );
  }

  Widget _buildTelegramIdField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Telegram User ID',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _telegramIdController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Enter your Telegram User ID',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.telegram),
            helperText: 'You can get this from @userinfobot on Telegram',
          ),
          validator: _validateTelegramId,
        ),
      ],
    );
  }

  Widget _buildTrainsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Classes for Each Train',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        const Text(
          'Auto-selected: SL, 3E, 3A, 2S, CC (if available)',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(height: 16),
        ...widget.trains.map((train) => _buildTrainClassSelection(train)),
      ],
    );
  }

  Widget _buildTrainClassSelection(TrainData train) {
    final selectedClasses = _selectedClassesByTrain[train.trainNumber] ?? {};

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${train.trainNumber} - ${train.trainName}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Dep: ${train.departureTime}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Expanded(
                  child: Text(
                    'Arr: ${train.arrivalTime}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Expanded(
                  child: Text(
                    train.duration,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: train.avlClasses.map((cls) {
                final isSelected = selectedClasses.contains(cls);
                final isAutoSelected = train.autoSelectedClasses.contains(cls);

                return FilterChip(
                  label: Text(cls),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedClassesByTrain[train.trainNumber]!.add(cls);
                      } else {
                        _selectedClassesByTrain[train.trainNumber]!.remove(cls);
                      }
                    });
                  },
                  backgroundColor: isAutoSelected ? Colors.blue.shade50 : null,
                  selectedColor: Colors.blue.shade100,
                  checkmarkColor: Colors.blue.shade700,
                  labelStyle: TextStyle(
                    color: isSelected ? Colors.blue.shade700 : null,
                    fontWeight: isAutoSelected ? FontWeight.w600 : null,
                  ),
                );
              }).toList(),
            ),
            if (selectedClasses.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Selected: ${selectedClasses.join(', ')}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChartTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chart Preparation Times',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 16),
        if (!_isLoadingChartTimes)
          ElevatedButton.icon(
            onPressed: _fetchAllChartTimes,
            icon: const Icon(Icons.access_time, size: 16),
            label: const Text('Fetch All Chart Times'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        const SizedBox(height: 16),
        if (_isLoadingChartTimes)
          const Center(
            child: Column(
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 8),
                Text('Fetching chart times for all trains...'),
              ],
            ),
          )
        else if (_chartTimesByTrain.isNotEmpty)
          ...widget.trains.map((train) => _buildChartTimeCard(train))
        else
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'Chart times are required to complete the selection. Please fetch them first.',
              style: TextStyle(color: Colors.orange),
            ),
          ),
      ],
    );
  }

  Widget _buildChartTimeCard(TrainData train) {
    final chartTime = _chartTimesByTrain[train.trainNumber];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${train.trainNumber} - ${train.trainName}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    chartTime ?? 'Not fetched',
                    style: TextStyle(
                      color: chartTime != null ? Colors.green.shade700 : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            if (chartTime != null)
              Icon(Icons.check_circle, color: Colors.green.shade700, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _isSubmitting ? null : _submitSelection,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: _isSubmitting
              ? const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    SizedBox(width: 12),
                    Text('Processing...'),
                  ],
                )
              : const Text(
                  'Complete Selection',
                  style: TextStyle(fontSize: 16),
                ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select ${widget.trains.length} Trains'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildRouteInfo(),
                    const SizedBox(height: 24),
                    _buildTelegramIdField(),
                    const SizedBox(height: 24),
                    _buildTrainsList(),
                    const SizedBox(height: 24),
                    _buildChartTimeSection(),
                  ],
                ),
              ),
            ),
            _buildBottomActionBar(),
          ],
        ),
      ),
    );
  }
}
